import {
  EparakstsAuthorization<PERSON>ethod,
  LoginPollStatus,
} from 'api/core/generated';
import {
  ABBREVIATIONS_BY_LANGUAGES_MAP,
  AppLoginMethods,
  AppProductType,
  AppRoutePaths,
  AppSearchParams,
  EPARAKSTS_BANKLINK_LOGIN_PARAMS_BY_PRODUCT_TYPE_MAP,
  GoogleAnalyticsEvents,
  LocalStorageKeys,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
  LoginPageViewTypes,
  PageAttributeNames,
  PAYSERA_PAYMENT_STATUSES,
  PURCHASE_FLOW_LOG_ACTIONS,
  SmallLoanRoutePaths,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useCheckUpdateUserLanguage } from 'hooks/use-check-update-user-language';
import { useEParakstsLogin } from 'hooks/use-eparaksts-login';
import { useGetBanks } from 'hooks/use-get-banklinks';
import { useGetPageAttributesSuspense } from 'hooks/use-get-page-attributes-suspense';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { useLoginByBanklink } from 'hooks/use-login-by-banklink';
import { useLoginByIdCard } from 'hooks/use-login-by-id-card';
import { useLoginByMagicLink } from 'hooks/use-login-by-magic-link';
import { useLoginByMobileId } from 'hooks/use-login-by-mobileId';
import { useLoginByPassword } from 'hooks/use-login-by-password';
import { useLoginBySmartId } from 'hooks/use-login-by-smartId';
import { useLoginMethodButtons } from 'hooks/use-login-method-buttons';
import { usePayseraLoginPoll } from 'hooks/use-paysera-login-poll';
import { useRudderStack } from 'hooks/use-rudderstack';
import { useEffectOnce, useLocalStorage } from 'hooks/utils';
import { useEffect, useMemo, useState } from 'react';
import type { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  createSearchParams,
  useLocation,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  convertPageAttributeNamesToObject,
  extractValidationErrors,
  getFilteredUrlSearchParamsObject,
  removeFromStorage,
  setToStorage,
  storeEParakstsOriginalUri,
} from 'services';

export const useLoginPageLogic = () => {
  const { identify } = useRudderStack();
  const [searchParams] = useSearchParams();
  const { search } = useLocation();
  const navigate = useNavigate();
  const magicLinkToken = searchParams.get(AppSearchParams.magicLinkToken);
  const eParakstsCode = searchParams.get(AppSearchParams.eParakstsCode);
  const payseraLoginPaymentStatus = searchParams.get(
    AppSearchParams.payseraLoginPaymentStatus,
  );
  const applicationReferenceKey = searchParams.get(
    AppSearchParams.referenceKey,
  );

  const { t, i18n } = useTranslation(LocizeNamespaces.errors);
  const [storedQueryParams, storeQueryParams] = useLocalStorage<Record<
    string,
    string
  > | null>(LocalStorageKeys.storedQueryParams, null);

  const [pageViewType, setPageViewType] = useState(LoginPageViewTypes.login);
  const [
    successfulLoginCallbacksProcessing,
    setSuccessfulLoginCallbacksProcessing,
  ] = useState(false);

  const {
    quietUserRefetch,
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
    applicationPrivateInfo,
    getApplicationPrivateInfo,
    trackGoogleAnalyticsEvent,
    application,
    user,
  } = useRootContext();

  const { application_id } = applicationPrivateInfo ?? {};

  const { logAction } = useLogApplicationAction();

  const checkUpdateUserLanguage = useCheckUpdateUserLanguage();

  const { pageAttributes, pageAttributesLoading } =
    useGetPageAttributesSuspense();
  const { getBanklinks, banklinks } = useGetBanks();
  const { loginByPassword, loginByPasswordProcessing, loginByPasswordError } =
    useLoginByPassword();
  const {
    loginByMagicLink,
    loginByMagicLinkProcessing,
    loginByMagicLinkError,
  } = useLoginByMagicLink();
  const {
    loginByBankLink,
    loginByBanklinkError,
    loginByBankLinkData,
    loginByBanklinkProcessing,
    banklinkLoginRedirectUrl,
    banklinkLoginAcceptUrl,
    banklinkLoginCancelUrl,
    banklinkLoginStatusError,
  } = useLoginByBanklink();
  const {
    startPayseraLoginStatusPolling,
    stopPayseraLoginStatusPolling,
    payseraLoginStatus,
    payseraLoginStatusError,
  } = usePayseraLoginPoll();
  const {
    loginBySmartId,
    loginBySmartIdError,
    smartIdChallengeData,
    getSmartIdState,
    authorizedBySmartId,
    stopSmartIdStatePolling,
    loginBySmartIdProcessing,
    smartIdPollError,
  } = useLoginBySmartId();
  const {
    loginByMobileId,
    loginByMobileIdError,
    mobileIdChallengeData,
    getMobileIdState,
    authorizedByMobileId,
    stopMobileIdStatePolling,
    loginByMobileIdProcessing,
    mobileIdPollError,
  } = useLoginByMobileId();
  const { loginByIdCard, loginByIdCardProcessing, loginByIdCardError } =
    useLoginByIdCard();
  const {
    executeEParakstsLoginChallenge,
    eParakstsLoginChallengeProcessing,
    eParakstsLoginChallengeError,
    eParakstsLoginProcessing,
    loginByEParaksts,
    eParakstsLoginError,
  } = useEParakstsLogin();
  const { loginMethodButtons, selectedLoginMethod } = useLoginMethodButtons();

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const loginProcessingMap = {
    [AppLoginMethods.mobileId]: loginByMobileIdProcessing,
    [AppLoginMethods.smartId]: loginBySmartIdProcessing,
    [AppLoginMethods.idCard]: loginByIdCardProcessing,
    [AppLoginMethods.password]: loginByPasswordProcessing,
    [AppLoginMethods.banklink]:
      loginByBanklinkProcessing || Boolean(loginByBankLinkData),
    [AppLoginMethods.eParakstsMobile]: eParakstsLoginChallengeProcessing,
    [AppLoginMethods.eParakstsSmartCard]: eParakstsLoginChallengeProcessing,
  };

  const loginErrorsByMethodMap = {
    [AppLoginMethods.mobileId]: loginByMobileIdError,
    [AppLoginMethods.smartId]: loginBySmartIdError,
    [AppLoginMethods.idCard]: loginByIdCardError,
    [AppLoginMethods.password]: loginByPasswordError,
    [AppLoginMethods.banklink]: loginByBanklinkError,
    [AppLoginMethods.eParakstsMobile]: eParakstsLoginChallengeError,
    [AppLoginMethods.eParakstsSmartCard]: eParakstsLoginChallengeError,
  };

  const loginFLowErrorsByMethodMap = {
    [AppLoginMethods.mobileId]: Boolean(
      loginByMobileIdError || mobileIdPollError,
    ),
    [AppLoginMethods.smartId]: Boolean(loginBySmartIdError || smartIdPollError),
    [AppLoginMethods.idCard]: Boolean(loginByIdCardError),
    [AppLoginMethods.password]: Boolean(loginByPasswordError),
    [AppLoginMethods.banklink]:
      Boolean(
        banklinkLoginStatusError ||
          loginByBanklinkError ||
          payseraLoginStatusError ||
          loginByMagicLinkError,
      ) || payseraLoginPaymentStatus === PAYSERA_PAYMENT_STATUSES.failed,
    [AppLoginMethods.eParakstsMobile]: Boolean(
      eParakstsLoginError || eParakstsLoginChallengeError,
    ),
    [AppLoginMethods.eParakstsSmartCard]: Boolean(
      eParakstsLoginError || eParakstsLoginChallengeError,
    ),
  };

  const loginValidationErrors = extractValidationErrors(
    loginErrorsByMethodMap[selectedLoginMethod],
  );

  const loginMethodButtonsConfig = loginMethodButtons.filter(
    ({ method }) => visiblePageAttributes[method],
  );

  const returnToLoginView = () => {
    setPageViewType(LoginPageViewTypes.login);
  };

  const stopLoginPolling = () => {
    if (selectedLoginMethod === AppLoginMethods.smartId) {
      stopSmartIdStatePolling();
    }

    if (selectedLoginMethod === AppLoginMethods.mobileId) {
      stopMobileIdStatePolling();
    }

    returnToLoginView();
  };

  const logLoginAction = (action: string, productId: number) => {
    // LOGGING ACTION
    return logAction({
      productId,
      action,
    });
  };

  const executeSuccessfulLoginCallbacks = async (loggingAction: string) => {
    setSuccessfulLoginCallbacksProcessing(true);

    try {
      await logLoginAction(loggingAction, application_id);
      await quietUserRefetch();
      checkUpdateUserLanguage();
      identify();
      await getApplicationPrivateInfo();
      await getPageUrlAndNavigate(true, {
        customCurrentPageUrl: `/${AppRoutePaths.SMALL_LOAN}/${SmallLoanRoutePaths.CHECKOUT}${search}`,
      });
      trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.loginCompleted);
    } finally {
      setSuccessfulLoginCallbacksProcessing(false);
    }
  };

  const checkBanklinkButtonDisabled = (method: AppLoginMethods) =>
    method === AppLoginMethods.banklink && !banklinks.length;

  const storeQueryParamsIfNeeded = () => {
    const queryParamsToStore = getFilteredUrlSearchParamsObject(
      EPARAKSTS_BANKLINK_LOGIN_PARAMS_BY_PRODUCT_TYPE_MAP[
        AppProductType.SMALL_LOAN
      ],
      searchParams,
    );

    if (Object.keys(queryParamsToStore).length) {
      storeQueryParams(queryParamsToStore);
    }
  };

  const loginViaSmartId = ({ pin }: FieldValues) => {
    return loginBySmartId({
      pin,
    }).then((result) => {
      if (result.data?.challenge?.session_id) {
        setPageViewType(LoginPageViewTypes.pinConfirmation);
        getSmartIdState({
          variables: { session_id: result.data.challenge.session_id },
        }).catch(() => {
          stopLoginPolling();
        });
      }
    });
  };

  const loginViaMobileId = ({ phone, pin }: FieldValues) => {
    return loginByMobileId({
      phone,
      pin,
    }).then((result) => {
      if (result.data?.challenge?.session_id) {
        setPageViewType(LoginPageViewTypes.pinConfirmation);
        getMobileIdState({
          variables: { session_id: result.data.challenge.session_id },
        }).catch(() => {
          stopLoginPolling();
        });
      }
    });
  };

  const loginViaPassword = ({ username, password }: FieldValues) => {
    return loginByPassword({
      username,
      password,
    }).then(({ data }) => {
      if (data?.success) {
        executeSuccessfulLoginCallbacks(
          PURCHASE_FLOW_LOG_ACTIONS.loggingInWithPassword,
        );
      }
    });
  };

  const loginViaIdCard = () => {
    setPageViewType(LoginPageViewTypes.idCard);

    return loginByIdCard().then((response) => {
      if (response?.ok) {
        executeSuccessfulLoginCallbacks(
          PURCHASE_FLOW_LOG_ACTIONS.loggingInWithIdCard,
        );
      } else {
        returnToLoginView();
      }
    });
  };

  const loginViaBanklink = ({ pin, payment_method_key }: FieldValues) => {
    return loginByBankLink({
      pin,
      payment_method_key,
      accept_url: banklinkLoginAcceptUrl,
      cancel_url: banklinkLoginCancelUrl,
      magic_login_url: banklinkLoginRedirectUrl,
      application_reference: applicationReferenceKey,
    }).then(({ data }) => {
      const redirectUrl = data?.challenge?.redirect_url;
      const sessionId = data?.challenge?.session_id;

      storeQueryParamsIfNeeded();

      if (sessionId) {
        setToStorage(LocalStorageKeys.sessionId, sessionId);
      }

      if (redirectUrl) {
        window.location.href = redirectUrl;
      }
    });
  };

  const loginViaEParaksts = (method: EparakstsAuthorizationMethod) => {
    storeEParakstsOriginalUri();

    return executeEParakstsLoginChallenge(method).then(({ data }) => {
      const redirectUrl = data?.challenge?.redirect_url;
      if (redirectUrl) {
        window.location.href = redirectUrl;
      }
    });
  };

  const onLoginFormSubmit = (formFieldValues: FieldValues) => {
    switch (selectedLoginMethod) {
      case AppLoginMethods.smartId:
        return loginViaSmartId(formFieldValues);
      case AppLoginMethods.mobileId:
        return loginViaMobileId(formFieldValues);
      case AppLoginMethods.idCard:
        return loginViaIdCard();
      case AppLoginMethods.password:
        return loginViaPassword(formFieldValues);
      case AppLoginMethods.banklink:
        return loginViaBanklink(formFieldValues);
      case AppLoginMethods.eParakstsMobile:
        return loginViaEParaksts(EparakstsAuthorizationMethod.MOBILE);
      case AppLoginMethods.eParakstsSmartCard:
        return loginViaEParaksts(EparakstsAuthorizationMethod.SMARTCARD);
      default:
        break;
    }
  };

  useEffectOnce(() => {
    if (storedQueryParams) {
      const params = Object.fromEntries(Array.from(searchParams));
      navigate(
        {
          search: createSearchParams({
            ...params,
            ...storedQueryParams,
          }).toString(),
        },
        { replace: true },
      );
      storeQueryParams(null);
    }
  });

  useEffectOnce(() => {
    if (magicLinkToken) {
      loginByMagicLink(magicLinkToken).then(({ data }) => {
        if (data?.success) {
          executeSuccessfulLoginCallbacks(
            PURCHASE_FLOW_LOG_ACTIONS.loggingInWithPaysera,
          );
        }
      });
    }
  });

  useEffect(() => {
    if (eParakstsCode) {
      loginByEParaksts().then(({ data }) => {
        if (data?.success?.is_authenticated) {
          executeSuccessfulLoginCallbacks(
            PURCHASE_FLOW_LOG_ACTIONS.loggingInWithEparaksts,
          );
        }
      });
    }
  }, [eParakstsCode]);

  useEffectOnce(() => {
    if (payseraLoginPaymentStatus === PAYSERA_PAYMENT_STATUSES.successful) {
      setPageViewType(LoginPageViewTypes.pending);
      startPayseraLoginStatusPolling().catch(() => {
        returnToLoginView();
      });
    } else if (payseraLoginPaymentStatus === PAYSERA_PAYMENT_STATUSES.failed) {
      toast.error(t(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError));
    }
  });

  useEffect(() => {
    switch (payseraLoginStatus) {
      case LoginPollStatus.SUCCESSFUL:
        stopPayseraLoginStatusPolling();
        removeFromStorage(LocalStorageKeys.sessionId);
        executeSuccessfulLoginCallbacks(
          PURCHASE_FLOW_LOG_ACTIONS.loggingInWithPaysera,
        );
        break;
      case LoginPollStatus.FAILED:
        stopPayseraLoginStatusPolling();
        removeFromStorage(LocalStorageKeys.sessionId);
        returnToLoginView();
        break;
      case LoginPollStatus.EMAIL_REQUEST:
        setPageViewType(LoginPageViewTypes.magic);
        break;
      case LoginPollStatus.MAGIC_LINK_SENT:
        setPageViewType(LoginPageViewTypes.pending);
        break;
      default:
        break;
    }
  }, [payseraLoginStatus]);

  useEffect(() => {
    if (
      visiblePageAttributes[PageAttributeNames.banklink] &&
      !banklinks.length
    ) {
      getBanklinks(ABBREVIATIONS_BY_LANGUAGES_MAP[i18n.language]);
    }
  }, [pageAttributes?.length]);

  useEffect(() => {
    if (authorizedBySmartId) {
      stopSmartIdStatePolling();
      executeSuccessfulLoginCallbacks(
        PURCHASE_FLOW_LOG_ACTIONS.loggingInWithSmartId,
      );
    }
  }, [authorizedBySmartId]);

  useEffect(() => {
    if (authorizedByMobileId) {
      stopMobileIdStatePolling();
      executeSuccessfulLoginCallbacks(
        PURCHASE_FLOW_LOG_ACTIONS.loggingInWithMobile,
      );
    }
  }, [authorizedByMobileId]);

  return {
    loginPageLoaded: !pageUrlAndNavigationProcessing && !pageAttributesLoading,
    processingLoginPage:
      loginProcessingMap[selectedLoginMethod] ||
      eParakstsLoginProcessing ||
      loginByMagicLinkProcessing ||
      successfulLoginCallbacksProcessing,
    smartIdChallengeId: smartIdChallengeData?.challenge_id || '',
    mobileIdChallengeId: mobileIdChallengeData?.challenge_id || '',
    selectedLoginMethod,
    banklinkOptions: banklinks,
    loginButtons: loginMethodButtonsConfig,
    loginButtonDisabled:
      loginProcessingMap[selectedLoginMethod] ||
      loginByMagicLinkProcessing ||
      eParakstsLoginProcessing,
    visiblePageAttributes,
    onLoginFormSubmit,
    checkBanklinkButtonDisabled,
    loginValidationErrors,
    pageViewType,
    setPageViewType,
    isLoginFlowWithError: loginFLowErrorsByMethodMap[selectedLoginMethod],
    stopLoginPolling,
    onIdCardLoginCancel: returnToLoginView,
    loginByIdCardProcessing,
    // if application already has user we don't want to go back on back button click
    backNavigationDirectionValue:
      !!application?.user_id && !user?.id ? null : false,
  };
};
