import {
  AppSigningMethods,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
  SpouseConsentPageViewTypes,
} from 'app-constants';
import { CircleLoader } from 'components/circle-loader/CircleLoader';
import { Typography } from 'components/typography';
import { useSpouseConsentPageContext } from 'context/small-loan';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout/FullScreenLayout';

export const SigningContractView = () => {
  const { spouseConsentPageViewType, spouseSigningMethod } =
    useSpouseConsentPageContext();
  const { t } = useTranslation(LocizeNamespaces.signing);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const [shouldShowDelayedMessage, setShouldShowDelayedMessage] =
    useState(false);

  useEffect(() => {
    setTimeout(() => {
      setShouldShowDelayedMessage(true);
    }, 10000);
  }, []);

  return (
    <FullScreenLayout>
      <CircleLoader className="scale-[70%] w-full min-[375px]:scale-100" />
      <div className="min-h-[6.25rem]">
        {spouseConsentPageViewType ===
          SpouseConsentPageViewTypes.signingContract && (
          <div
            className={cn(
              'text-center max-h-[100px] opacity-100 transition-all duration-1000 ease',
              shouldShowDelayedMessage && 'max-h-0 opacity-0',
            )}
          >
            <Typography variant="xs" className="text-center">
              {t(LOCIZE_SIGNING_TRANSLATION_KEYS.signingContractHeading)}
            </Typography>
            <Typography className="mt-4 text-center">
              {t(LOCIZE_SIGNING_TRANSLATION_KEYS.keepPageOpenDisclaimer)}
            </Typography>
          </div>
        )}
        {spouseSigningMethod === AppSigningMethods.smartId && (
          <div
            className={cn(
              'text-center max-h-[100px] opacity-100 transition-all duration-1000 ease',
              shouldShowDelayedMessage && 'max-h-0 opacity-0',
            )}
          >
            <>
              <Typography variant="xs" className="text-center">
                {t(LOCIZE_SIGNING_TRANSLATION_KEYS.preparingSigningHeading)}
              </Typography>

              {spouseSigningMethod === AppSigningMethods.smartId ? (
                <Typography className="mt-4 text-center">
                  {t(LOCIZE_SIGNING_TRANSLATION_KEYS.openSmartIdOnYourDevice)}
                </Typography>
              ) : spouseSigningMethod === AppSigningMethods.mobileId ? (
                <Typography className="mt-4 text-center">
                  {t(LOCIZE_SIGNING_TRANSLATION_KEYS.openMobileIdOnYourDevice)}
                </Typography>
              ) : (
                <Typography className="mt-4 text-center">
                  {t(LOCIZE_SIGNING_TRANSLATION_KEYS.keepPageOpenDisclaimer)}
                </Typography>
              )}
            </>
          </div>
        )}
        <div
          className={cn(
            'text-center max-h-0 opacity-0 transition-all duration-1000 ease delay-700 flex flex-col gap-4',
            shouldShowDelayedMessage && 'max-h-[100px] opacity-100',
          )}
        >
          <Typography variant="xs">
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.defaultDelayedMessageTitle)}
          </Typography>
          {spouseSigningMethod === AppSigningMethods.smartId ? (
            <Typography variant="text-s">
              {t(LOCIZE_SIGNING_TRANSLATION_KEYS.openSmartIdOnYourDevice)}
            </Typography>
          ) : spouseSigningMethod === AppSigningMethods.mobileId ? (
            <Typography variant="text-s">
              {t(LOCIZE_SIGNING_TRANSLATION_KEYS.openMobileIdOnYourDevice)}
            </Typography>
          ) : (
            <Typography variant="text-s">
              {tc(LOCIZE_COMMON_TRANSLATION_KEYS.defaultDelayedMessageText)}
            </Typography>
          )}
        </div>
      </div>
    </FullScreenLayout>
  );
};

export default SigningContractView;
