query CurrentApplication($reference_key: String!) {
  application: current_application(reference_key: $reference_key) {
    id
    type
    user_id
    requested_amount
    reference_key
    is_test
    merchant_id
    return_url
    cancel_url
    simple_eligibility_status
    application_user_info_id
    for_private_person
    schedule_type
    free_hp_enabled
    add_legal_person_to_invoice
    for_private_person
    from_retail
    is_instant_payout
    is_instant_payout_available
    pre_signing_conversion_offer {
      initial_schedule_type
    }
    legal_person_info {
      name
      email
      phone
      address
      legal_person_score {
        legal_person_id
        legal_person {
          registry_code
        }
      }
    }
    credit_info {
      monthly_payment
      down_payment
      net_total
      fixed_period_months
      period_months
      min_down_payment
      fixed_contract_fee
      fixed_management_fee
      contract_fee
      management_fee
      annual_pct_rate
    }
    merchant {
      settings {
        net_total_min
        net_total_max
        small_loan_is_paid_to_merchant
      }
      name
    }
    user {
      sign_in_method
      political_exposure
      conditions_agreement
      newsletter_agreement
      allow_pension_query
      language_abbr
      profile {
        overdue_debt
        spouse_overdue_debt
      }
    }
    user_info {
      id
      first_name
      last_name
      email
      phone
      address
      city
      post_code
      phone_area_code
      ultimate_beneficial_owner
      net_income_monthly
      monthly_living_expenses
      expenditure_monthly
      number_of_dependents
      employment_date
      future_reduced_earnings
      planning_new_debts
      purpose_of_loan
      iban
      occupation_category
      spouse_first_name
      spouse_last_name
      spouse_email
      spouse_phone
      spouse_phone_area_code
      spouse_pin
      spouse_employment_date
      spouse_net_income_monthly
      spouse_extra_income
      spouse_expenditure_monthly
      spouse_monthly_living_expenses
      spouse_expenditure_consumer_loan_monthly
    }
    campaign {
      interest_free_months
      payment_leave_enabled
      payment_leave_months
      fixed_contract_fee
      converting_schedule_fixed_contract_fee
    }
  }
}

query ApplicationByReference($reference_key: String!) {
  application: application_by_reference(reference_key: $reference_key) {
    id
    application_user_info_id
    reference_key
    user_id
    requested_amount
    is_test
    return_url
    cancel_url
    from_retail
    credit_info {
      monthly_payment
      down_payment
      net_total
      fixed_period_months
      period_months
      min_down_payment
    }
    merchant {
      settings {
        net_total_min
        net_total_max
      }
      direct_payment_gateways {
        provider
        enabled
      }
      name
    }
    user_info {
      id
      email
      first_name
      last_name
    }
    schedule_type
    free_hp_enabled
    campaign {
      interest_free_months
      payment_leave_enabled
      payment_leave_months
      fixed_contract_fee
      converting_schedule_fixed_contract_fee
    }
  }
}

query ApplicationByShortRef($short_reference: String!) {
  application: application_by_reference(short_reference: $short_reference) {
    id
    reference_key
    schedule_type
    application_reference {
      phone
      phone_area_code
    }
  }
}

query CreditSettings(
  $net_total: Float!
  $down_payment: Float
  $application_id: Int
  $merchant_id: Int
  $schedule_type: ApplicationScheduleType
  $consider_max_monthly_payment: Boolean
) {
  settings: calculate_credit_settings(
    merchant_id: $merchant_id
    net_total: $net_total
    down_payment: $down_payment
    application_id: $application_id
    schedule_type: $schedule_type
    consider_max_monthly_payment: $consider_max_monthly_payment
  ) {
    month
    contract_fee
    management_fee
    annual_pct_rate
    monthly_payment
    max_down_payment
    min_down_payment
  }
}

query SmallLoanCreditSettings(
  $net_total: Float!
  $application_id: Int
  $schedule_type: ApplicationScheduleType
  $approximate: Boolean
) {
  settings: calculate_credit_settings(
    net_total: $net_total
    application_id: $application_id
    schedule_type: $schedule_type
    approximate: $approximate
  ) {
    month
    monthly_payment
  }
}

query FirstDueAt($application_id: Int!, $has_payment_leave: Boolean!) {
  application_first_due_at(
    application_id: $application_id
    has_payment_leave: $has_payment_leave
  )
}

mutation UpdateCreditInfoOfApplication(
  $application_id: Int!
  $period_months: Int!
  $net_total: Float
  $down_payment: Float
  $reference_key: String
  $accept_conversion_offer: Boolean
) {
  application: update_application_credit(
    application_id: $application_id
    period_months: $period_months
    net_total: $net_total
    down_payment: $down_payment
    reference_key: $reference_key
    accept_conversion_offer: $accept_conversion_offer
  ) {
    id
  }
}

mutation UpdateUserInfoOfApplication(
  $application_id: Int!
  $email: String
  $phone: String
  $address: String
  $city: String
  $post_code: String
  $iban: String
) {
  update_application_user(
    application_id: $application_id
    email: $email
    phone: $phone
    address: $address
    city: $city
    post_code: $post_code
    iban: $iban
    reject_when_necessary: false
  ) {
    id
  }
}

query ApplicationPrivateLabel($reference_key: String!) {
  applicationInfo: application_private_label(reference_key: $reference_key) {
    logo_url
    background_url
    is_test
    application_id
    provider_name
    show_down_payment
    min_amount
    max_amount
  }
}

mutation UpdateUserInfoExtraOfApplication(
  $application_id: Int!
  $net_income_monthly: Float
  $expenditure_monthly: Float
  $monthly_living_expenses: Float
  $number_of_dependents: Int
  $ultimate_beneficial_owner: Boolean
  $planning_new_debts: Float
  $future_reduced_earnings: Float
  $employment_date: String
  $occupation_category: OccupationCategory
  $reject_when_necessary: Boolean
  $extra_income: Float
) {
  application: update_application_user_extra(
    application_id: $application_id
    net_income_monthly: $net_income_monthly
    expenditure_monthly: $expenditure_monthly
    number_of_dependents: $number_of_dependents
    monthly_living_expenses: $monthly_living_expenses
    ultimate_beneficial_owner: $ultimate_beneficial_owner
    planning_new_debts: $planning_new_debts
    future_reduced_earnings: $future_reduced_earnings
    employment_date: $employment_date
    occupation_category: $occupation_category
    reject_when_necessary: $reject_when_necessary
    extra_income: $extra_income
  ) {
    id
  }
}

mutation UpdateApplication(
  $application_id: Int!
  $for_private_person: Boolean!
  $schedule_type: ApplicationScheduleType!
  $legal_person_id: Int
  $add_legal_person_to_invoice: Boolean
) {
  application: update_application(
    application_id: $application_id
    for_private_person: $for_private_person
    schedule_type: $schedule_type
    legal_person_id: $legal_person_id
    add_legal_person_to_invoice: $add_legal_person_to_invoice
  ) {
    id
  }
}

mutation UpdateLegalPersonInfo(
  $application_id: Int!
  $legal_person_id: Int!
  $email: String!
  $phone: String!
  $address: String!
  $reject_when_necessary: Boolean
) {
  application: update_application_legal_person(
    application_id: $application_id
    legal_person_id: $legal_person_id
    email: $email
    address: $address
    phone: $phone
    reject_when_necessary: $reject_when_necessary
  ) {
    id
  }
}

mutation UpdateApplicationCampaign(
  $applicationId: Int!
  $referenceKey: String
  $paymentLeaveEnabled: Boolean!
) {
  campaign: update_application_campaign(
    application_id: $applicationId
    reference_key: $referenceKey
    payment_leave_enabled: $paymentLeaveEnabled
  ) {
    application_id
    payment_leave_months
    payment_leave_enabled
    interest_free_months
    converting_schedule_months
    converting_schedule_name
  }
}

fragment ApplicationMerchantCampaigns on Application {
  id
  type
  schedule_type
  requested_amount
  for_private_person
  reference_key
  merchant {
    id
    logo_path
    settings {
      net_total_min
      net_total_max
    }
    campaign {
      converting_schedule_logo_url
      pay_later_logo_url
      esto_pay_logo_url
      regular_hp_enabled
      converting_schedule_enabled
      pay_later_enabled
      esto_pay_enabled
      converting_schedule_net_total_min
      converting_schedule_net_total_max
      converting_schedule_months
      pay_later_net_total_min
      pay_later_net_total_max
      esto_pay_net_total_min
      esto_pay_net_total_max
      converting_schedule_name
      pay_later_name
      esto_pay_name
    }
  }
}

query CurrentApplicationMerchantCampaigns($reference_key: String!) {
  application: current_application(reference_key: $reference_key) {
    ...ApplicationMerchantCampaigns
  }
}

query ApplicationMerchantCampaignsByReference($reference_key: String!) {
  application: application_by_reference(reference_key: $reference_key) {
    ...ApplicationMerchantCampaigns
  }
}

mutation UpdateApplicationSchedule(
  $application_id: Int!
  $reference_key: String
  $for_private_person: Boolean!
  $schedule_type: ApplicationScheduleType!
  $period_months: Int
) {
  application: update_application(
    application_id: $application_id
    reference_key: $reference_key
    for_private_person: $for_private_person
    schedule_type: $schedule_type
    period_months: $period_months
  ) {
    id
    schedule_type
  }
}

mutation UpdateApplicationPayoutMethod(
  $application_id: Int!
  $for_private_person: Boolean!
  $schedule_type: ApplicationScheduleType!
  $is_instant_payout: Boolean
) {
  application: update_application(
    application_id: $application_id
    for_private_person: $for_private_person
    is_instant_payout: $is_instant_payout
    schedule_type: $schedule_type
  ) {
    id
    is_instant_payout
  }
}

query GetScheduleTypeSettings($schedule_type: ApplicationScheduleType) {
  schedule_type_settings(schedule_type: $schedule_type) {
    possible_periods
  }
}
